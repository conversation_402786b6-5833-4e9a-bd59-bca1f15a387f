<?php

/**
 * Heading & Content
 */

$heading = get_sub_field('heading');
$heading_large = get_sub_field('heading_large');
$copy = get_sub_field('copy');
$copy_wide = get_sub_field('copy_wide');

// Build heading classes
$heading_classes = ['heading-content__heading', 'heading-light', 'heading-underlined', 'text-weight-regular'];
if ($heading_large) {
    $heading_classes[] = 'h2-large';
}

// Build content classes
$content_classes = ['heading-content__content', 'centre'];
if ($copy_wide) {
    $content_classes[] = 'copy-wide';
}

?>

<section class="heading-content">
    <div class="heading-content__inner" data-aos="fade">
        <div class="container heading-content__container">
            <div class="<?php echo implode(' ', $content_classes); ?>">
                <?php if($heading) : ?>
                    <h2 class="<?php echo implode(' ', $heading_classes); ?>"><?php echo $heading; ?></h2>
                <?php endif; ?>
                <?php if($copy) : ?>
                    <div class="heading-content__copy content-area copy-large">
                        <?php echo $copy; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section><!-- .heading-content -->
