<?php

$page_obj = get_queried_object();

get_header();

get_template_part('lib/blocks/page-header');

?>


<?php


$theading = get_field('region_heading', $page_obj);
$tcopy = get_field('region_copy', $page_obj);
$top_links = 'region_top_links';

if (is_tax('holiday-type')) {
    $theading = get_field('holiday_type_heading', $page_obj);
    $tcopy = get_field('holiday_type_copy', $page_obj);
    $top_links = 'holiday_type_top_links';
}

?>
<div class="text-block centre">
    <div class="text-block__container container">
        <div class="text-block__inner-container inner-container">
            <?php if ($theading) : ?>
                <h2 class="text-block__heading heading-light heading-underlined text-weight-regular"><?php echo $theading; ?></h2>
            <?php endif; ?>
            <?php if ($tcopy) : ?>
                <div class="text-block__copy content-area copy-large">
                    <?php echo $tcopy; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php get_template_part('lib/components/results-cta-top'); ?>

<?php get_template_part('lib/components/featured-holidays-panel'); ?>

<?php if(!isset($_GET['type'])): ?>
<?php
// Check if there are any valid links before showing the filter-links div
$valid_links = [];
if (have_rows($top_links, $page_obj)) {
    while (have_rows($top_links, $page_obj)) : the_row();
        $link = get_sub_field('link');
        if ($link && !empty($link['url']) && !empty($link['title'])) {
            $valid_links[] = $link;
        }
    endwhile;
}

// Only show the filter-links div if there are valid links
if (!empty($valid_links)) : ?>
    <div class="filter-links centre">
        <div class="container filter-links__container">
            <div class="filter-links__text">
                <span><?php _e('Refine your search:', 'absoluteescapes'); ?></span>
            </div>
            <div class="filter-links__links">
                <?php foreach ($valid_links as $link) : ?>
                    <div class="filter-links__link-wrapper">
                        <a href="<?php echo $link['url']; ?>" <?php echo ($link['target']) ? 'target="_blank"' : ''; ?>
                           class="filter-links__link button button--hollow-grey"><?php echo $link['title']; ?></a>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php endif; ?>

<?php

$args = [
    'post_type' => 'holiday',
    'post_status' => 'publish',
    'posts_per_page' => -1
];

if (is_tax('holiday-regions')) {
    $args['tax_query'] = [
        [
            'taxonomy' => 'holiday-regions',
            'field' => 'slug',
            'terms' => $page_obj->slug,
        ]
    ];
}

if (is_tax('holiday-type')) {
    $args['tax_query'] = [
        [
            'taxonomy' => 'holiday-type',
            'field' => 'slug',
            'terms' => $page_obj->slug,
        ]
    ];
}

if (isset($_GET['durationmin']) && isset($_GET['durationmax'])) {

    $meta_query = array(

        array(
            'key' => 'holiday_maximum_duration',
            'type' => 'NUMERIC',
            'value' => $_GET['durationmin'],
            'compare' => '>=',
        ),
        array(
            'key' => 'holiday_minimum_duration',
            'type' => 'NUMERIC',
            'value' => $_GET['durationmax'],
            'compare' => '<=',
        )
    );


    $args['meta_query'] = $meta_query;
}

if (isset($_GET['region']) || isset($_GET['type'])) {


    if (isset($_GET['region'])) {
        $tax_region = array(
            'taxonomy' => 'holiday-regions',
            'field' => 'slug',
            'terms' => $_GET['region'],
        );

        $args['tax_query'][] = $tax_region;
    }


    if (isset($_GET['type'])) {
        $tax_type = array(
            'taxonomy' => 'holiday-type',
            'field' => 'slug',
            'terms' => $_GET['type'],
        );

        $args['tax_query'][] = $tax_type;
    }

}

// Set the default sort
$current_sort = 'most-popular';
if (isset($_GET['sort'])) {
    $current_sort = $_GET['sort'];
}

switch ($current_sort) {
    case 'most-popular':
        $args['meta_key'] = 'post_views_count';
        $args['orderby'] = 'meta_value_num';
        $args['order'] = 'DESC';
        break;
    case 'price-lowest':
        $args['meta_key'] = 'sort_price';
        $args['orderby'] = 'meta_value_num';
        $args['order'] = 'ASC';
        break;
    case 'price-highest':
        $args['meta_key'] = 'sort_price';
        $args['orderby'] = 'meta_value_num';
        $args['order'] = 'DESC';
        break;
    case 'shortest-duration':
        $args['meta_key'] = 'holiday_minimum_duration';
        $args['orderby'] = 'meta_value_num';
        $args['order'] = 'ASC';
        break;
    case 'longest-duration':
        $args['meta_key'] = 'holiday_maximum_duration';
        $args['orderby'] = 'meta_value_num';
        $args['order'] = 'DESC';
        break;
}

//$hquery = new WP_QUERY($args);

$args['posts_per_page'] = -1;


$mquery = new WP_Query($args);

unset($args['posts_per_page']);
$paged = ( get_query_var( 'paged' ) ) ? get_query_var( 'paged' ) : 1;
$args['paged'] = $paged;
$lquery = new WP_Query($args);
?>
<div id="holidaysResults" class="holidays-results">
    <div class="holidays-results__inner">
                    <div class="holidays-results__section-heading">
                        <h2 class="holidays-results__heading heading-light heading-underlined text-weight-regular"><?php _e('All holidays', 'absoluteescapes'); ?></h2>
                    </div>
        <div class="container holidays-results__container">
            <div class="row holidays-results__row">
                <div class="col-lg-3 holidays-results__col holidays-results__col--filter">
                    <div class="holidays-results__filter-triggers">
                        <div class="holidays-results__filter-trigger-wrapper">
                            <a href="#" id="filterTrigger"
                               class="holidays-results__button button button--alt">Filter</a>
                        </div>
                        <div class="holidays-results__sort-by-trigger-wrapper"></div>
                    </div>
                    <div class="holidays-results__filter sidebar__inner">
                        <?php get_component('filter', $mquery); ?>
                    </div>
                </div>
                <div class="col-lg-9 holidays-results__col holidays">

                    <div class="holidays-results__tabs-bar">
                        <div class="holidays-results__order">
                            <div class="holidays-results__results">
                                <span><?php _e('Showing ', 'absoluteescapes'); ?><?php echo $mquery->post_count; ?><?php _e(' holiday(s)', 'absoluteescapes'); ?></span>
                            </div>
                            <div class="holidays-results__sort-by-wrapper">
                                <div class="holidays-results__sort-by">
                                    <span class="holidays-results__sort-by-text"><?php _e('Sort by:', 'absoluteescapes'); ?></span>
                                    <div class="holidays-results__sort-by-select-wrapper select-wrapper">
                                        <select id="orderDropdown">
                                            <option value="most-popular"
                                                    <?php if ($current_sort === 'most-popular') : ?>selected<?php endif; ?>>
                                                Most popular
                                            </option>
                                            <option value="price-lowest"
                                                    <?php if ($current_sort === 'price-lowest') : ?>selected<?php endif; ?>>
                                                Price (Lowest)
                                            </option>
                                            <option value="price-highest"
                                                    <?php if ($current_sort === 'price-highest') : ?>selected<?php endif; ?>>
                                                Price (Highest)
                                            </option>
                                            <option value="shortest-duration"
                                                    <?php if ($current_sort === 'shortest-duration') : ?>selected<?php endif; ?>>
                                                Duration (Shortest)
                                            </option>
                                            <option value="longest-duration"
                                                    <?php if ($current_sort === 'longest-duration') : ?>selected<?php endif; ?>>
                                                Duration (Longest)
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="holidays-results__tabs">
                            <div id="listTab" class="holidays-results__tab active">
                                <span><?php _e('List view', 'absoluteescapes'); ?></span>
                            </div>
                            <div id="mapTab" class="holidays-results__tab">
                                <span><?php _e('Map view', 'absoluteescapes'); ?></span>
                            </div>
                        </div>

                    </div>
                    <?php if ($mquery->have_posts()) : ?>
                        <div class="holidays-results__map">
                            <div class="acf-map">
                                <?php while ($mquery->have_posts()) : $mquery->the_post();

                                    $location = get_field('holiday_location');

                                    ?>
                                    <div class="marker" data-lat="<?php echo $location['lat']; ?>"
                                         data-lng="<?php echo $location['lng']; ?>">
                                        <a href="<?php echo get_the_permalink(); ?>" class="marker__link">
                                            <div class="marker__gallery">
                                                <div class="marker__gallery-image"
                                                     style="background-image: url(<?php echo get_the_post_thumbnail_url(); ?>);"></div>
                                            </div>
                                            <div class="marker__text">
                                                <h5 class="marker__title"><?php echo get_the_title(get_the_ID()); ?></h5>
                                                <h5 class="marker__price"><?php echo do_shortcode(do_shortcode(get_field('holiday_price'))); ?></h5>
                                                <?php

                                                $mtypes = get_the_terms(get_the_ID(), 'holiday-type');
                                                $mDays = get_field('holiday_minimum_duration');
                                                $mDistance = get_field('holiday_distance');

                                                ?>

                                                <div class="marker__items">
                                                    <?php if ($mtypes) : ?>
                                                        <div class="marker__item">
                                                            <?php foreach ($mtypes as $type) : ?>
                                                                <?php

                                                                $icon = get_field('holiday_type_icon', $type);
                                                                $icon_fa = get_field('holiday_type_icon_fa', $type);
                                                                $colour = get_field('holiday_type_colour', $type);

                                                                if($type->parent) {
                                                                    continue;
                                                                }

                                                                ?>

                                                                <div class="holidays__type cat">
                                                                    <?php if ($icon) : ?>
                                                                        <span class="holidays__icon cat__icon"
                                                                              style="background-color: <?php echo ($colour) ? $colour : '#3e5056'; ?>"><img
                                                                                    src="<?php echo $icon['url']; ?>"
                                                                                    alt="<?php echo $icon['alt']; ?>"></span>
                                                                    <?php else : ?>
                                                                        <?php if ($icon_fa) : ?>
                                                                            <span class="holidays__icon cat__icon"
                                                                                  style="background-color: <?php echo ($colour) ? $colour : '#3e5056'; ?>"><i
                                                                                        class="<?php echo $icon_fa; ?>"></i></span>
                                                                        <?php endif; ?>
                                                                    <?php endif; ?>
                                                                    <span class="holidays__type-text cat__text"><?php echo $type->name; ?></span>
                                                                </div>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if ($mDays) : ?>
                                                        <div class="marker__item">
                                                            <div class="cat">
                                                                <span class="cat__icon"
                                                                      style="background-color: #3e5056;"><i
                                                                            class="fal fa-calendar-alt"></i></span>
                                                                <span class="cat__text"><?php echo $mDays . ' ' . __('Nights', 'absoluteescapes'); ?></span>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if ($mDistance) : ?>
                                                        <div class="marker__item">
                                                            <div class="cat">
                                                                <span class="cat__icon"
                                                                      style="background-color: #3e5056;"><i
                                                                            class="fal fa-wave-sine"></i></span>
                                                                <span class="cat__text"><?php echo $mDistance; ?></span>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                <?php endwhile;
                                wp_reset_postdata(); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    <?php if($lquery->have_posts()) : ?>
                        <div class="holidays-results__posts active inf-posts">
                          <?php while ($lquery->have_posts()) : $lquery->the_post(); ?>
                                <?php

                                $price = rtrim(do_shortcode(do_shortcode(get_field('holiday_price', get_the_ID()))));
                                $price_suffix_short = get_field('holiday_price_suffix_short', get_the_ID());
                                $distance = get_field('holiday_distance', get_the_ID());
                                $min_duration = get_field('holiday_minimum_duration', get_the_ID());
                                $max_duration = get_field('holiday_maximum_duration', get_the_ID());
                                if($max_duration == 999) {
                                    $max_duration = '';
                                }

                                if($min_duration === $max_duration) {
                                    $max_duration = '';
                                }
                                $types = get_the_terms(get_the_ID(), 'holiday-type');

                                ?>

                                <div class="holidays__post inf-post">
                                    <div class="row holidays__post-row">
                                        <div class="col-md-4 holidays__post-col">
                                            <?php if (have_rows('holiday_gallery', get_the_ID())) : ?>
                                                <div class="holidays__gallery">
                                                    <?php while (have_rows('holiday_gallery', get_the_ID())) : the_row(); ?>
                                                        <?php 

                                                        $image = wp_get_attachment_image(get_sub_field('image'), 'carousel-small');

                                                        ?>

                                                        <?php if ($image) : ?>
                                                            <div class="holidays__image">
                                                                <?php echo $image; ?>
                                                            </div>
                                                        <?php endif; ?>

                                                    <?php endwhile; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-4 holidays__post-col">
                                            <div class="holidays__post-content">
                                                <a href="<?php echo get_the_permalink(); ?>">
                                                    <h4 class="holidays__title"><?php echo get_the_title(get_the_ID()); ?></h4>
                                                </a>
                                                <?php if ($price) : ?>
                                                    <h4 class="holidays__price h4-small">
                                                        <span><?php _e('From ', 'absoluteescapes'); ?><?php echo $price; ?><?php echo $price_suffix_short; ?></span>
                                                    </h4>
                                                <?php endif; ?>

                                                <?php if ($types) : ?>
                                                    <div class="holidays__types">
                                                        <?php foreach ($types as $type) : ?>
                                                            <?php

                                                            $icon = get_field('holiday_type_icon', $type);
                                                            $icon_fa = get_field('holiday_type_icon_fa', $type);
                                                            $colour = get_field('holiday_type_colour', $type);


                                                            if($type->parent) {
                                                                continue;
                                                            }


                                                            ?>

                                                            <div class="holidays__type cat">
                                                                <?php if ($icon) : ?>
                                                                    <span class="holidays__icon cat__icon"
                                                                          style="background-color: <?php echo ($colour) ? $colour : '#3e5056'; ?>"><img
                                                                                src="<?php echo $icon['url']; ?>"
                                                                                alt="<?php echo $icon['alt']; ?>"></span>
                                                                <?php else : ?>
                                                                    <?php if ($icon_fa) : ?>
                                                                        <span class="holidays__icon cat__icon"
                                                                              style="background-color: <?php echo ($colour) ? $colour : '#3e5056'; ?>"><i
                                                                                    class="<?php echo $icon_fa; ?>"></i></span>
                                                                    <?php endif; ?>
                                                                <?php endif; ?>
                                                                <span class="holidays__type-text cat__text"><?php echo $type->name; ?></span>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                <?php endif; ?>

                                                <?php if ($distance) : ?>
                                                    <div class="holidays__text">
                                                        <span><?php _e('Distance ', 'absoluteescapes'); ?><?php echo $distance; ?></span>
                                                    </div>
                                                <?php endif; ?>

                                                <?php if ($min_duration) : ?>
                                                    <div class="holidays__text">
                                                        <span><?php _e('Duration ', 'absoluteescapes'); ?><?php echo $min_duration; ?><?php if($max_duration) : ?><?php echo ($max_duration) ? ' - ' . $max_duration : ''; ?><?php endif; ?><?php _e(' Nights', 'absoluteescapes'); ?></span>
                                                    </div>
                                                <?php endif; ?>

                                            </div>
                                        </div>
                                        <div class="col-md-4 holidays__post-col">
                                            <?php if (have_rows('holiday_key_points', get_the_ID())) : ?>
                                                <div class="holidays__key-points">
                                                    <ul class="holidays__key-points-list">
                                                        <?php while (have_rows('holiday_key_points', get_the_ID())) : the_row(); ?>
                                                            <?php

                                                            $point = get_sub_field('point');

                                                            ?>

                                                            <?php if ($point) : ?>
                                                                <li class="holidays__key-point">
                                                                    <span><?php echo $point; ?></span>
                                                                </li>
                                                            <?php endif; ?>

                                                        <?php endwhile; ?>
                                                    </ul>
                                                </div>
                                            <?php endif; ?>
                                            <div class="holidays__link-wrapper">
                                                <a href="<?php echo get_the_permalink(get_the_ID()); ?>"
                                                   class="holidays__link button button--alt-arrow"><?php _e('Find out more', 'absoluteescapes'); ?>
                                                    <i class="fas fa-chevron-right"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile;
                            wp_reset_postdata(); ?>
                        </div>

                        <?php if (get_next_posts_link()) : ?>
                            <div class="blog-posts__pagination">
                                <div class="blog-posts__infinite-scroll">
                                    <button type="button" class="button button--alt button--link-plain button-inf"><span
                                                class="text"><?php _e('Load more', 'classroommonitor'); ?></span> <i
                                                class="fas fa-chevron-down"></i></button>
                                    <div class="page-load-status"><span class="loader infinite-scroll-request"></span>
                                    </div>
                                </div>
                                <div class="next-posts-link"><?php next_posts_link('Next page'); ?></div>
                            </div>
                        <?php endif; ?>
                    <?php else : ?>

                        <div class="no-results centre">
                            <h3 class="text-uppercase"><strong>No Results Found.</strong></h3>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php get_template_part( 'lib/components/steps-ahead-panel'); ?>

<?php get_template_part( 'lib/components/results-cta-bottom'); ?>

<script type="text/javascript">

    (function ($) {

        /*
        *  new_map
        *
        *  This function will render a Google Map onto the selected jQuery element
        *
        *  @type	function
        *  @date	8/11/2013
        *  @since	4.3.0
        *
        *  @param	$el (jQuery element)
        *  @return	n/a
        */

        function new_map($el) {

            // var
            var $markers = $el.find('.marker');


            // vars
            var args = {
                zoom: 16,
                center: new google.maps.LatLng(0, 0),
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                disableDefaultUI: true
            };


            // create map
            var map = new google.maps.Map($el[0], args);


            // add a markers reference
            map.markers = [];


            // add markers
            $markers.each(function () {

                add_marker($(this), map);

            });


            // center map
            center_map(map);


            // return
            return map;

        }

        /*
        *  add_marker
        *
        *  This function will add a marker to the selected Google Map
        *
        *  @type	function
        *  @date	8/11/2013
        *  @since	4.3.0
        *
        *  @param	$marker (jQuery element)
        *  @param	map (Google Map object)
        *  @return	n/a
        */

        function add_marker($marker, map) {

            // var
            var latlng = new google.maps.LatLng($marker.attr('data-lat'), $marker.attr('data-lng'));

            var icon = '<?php echo get_stylesheet_directory_uri() . '/dist/img/marker.png'; ?>';

            // create marker
            var marker = new google.maps.Marker({
                position: latlng,
                map: map,
                icon: icon
            });

            // add to array
            map.markers.push(marker);

            // if marker contains HTML, add it to an infoWindow
            if ($marker.html()) {

                var infowindow = new InfoBox({
                    content: $marker.html()
                });

                var myOptions = {
                    content: $marker.html(),
                    disableAutoPan: true,

                    pixelOffset: new google.maps.Size(0, 0),
                    zIndex: null,
                    boxStyle: {
                        opacity: 1,
                    },
                    closeBoxMargin: "0 0 0 0",

                    infoBoxClearance: new google.maps.Size(1, 1),
                    isHidden: false,
                    pane: "floatPane",
                    enableEventPropagation: false
                };

                // create info window
                // var infowindow = new google.maps.InfoWindow({
                //     content		: $marker.html()
                // });

                // show info window when marker is clicked
                google.maps.event.addListener(marker, 'click', function () {

                    //     infowindow.open( map, marker );
                    infowindow.setOptions(myOptions);
                    infowindow.open(map, marker);
                });


            }

        }

        /*
        *  center_map
        *
        *  This function will center the map, showing all markers attached to this map
        *
        *  @type	function
        *  @date	8/11/2013
        *  @since	4.3.0
        *
        *  @param	map (Google Map object)
        *  @return	n/a
        */

        function center_map(map) {

            // vars
            var bounds = new google.maps.LatLngBounds();

            // loop through all markers and create bounds
            $.each(map.markers, function (i, marker) {

                var latlng = new google.maps.LatLng(marker.position.lat(), marker.position.lng());

                bounds.extend(latlng);

            });

            // only 1 marker?
            if (map.markers.length == 1) {
                // set center of map
                map.setCenter(bounds.getCenter());
                map.setZoom(16);
            } else {
                // fit to bounds
                map.fitBounds(bounds);
            }

            $('#mapTab').on('click', function () {

                $('.holidays-results__tab').removeClass('active');
                $(this).addClass('active');

                $('.holidays-results__map').addClass('active');
                $('.holidays-results__posts').removeClass('active');

                $('.blog-posts__pagination').hide();

                map.fitBounds(bounds);


            });

            $('#listTab').on('click', function () {

                $('.holidays-results__tab').removeClass('active');
                $(this).addClass('active');

                $('.holidays-results__map').removeClass('active');
                $('.holidays-results__posts').addClass('active');

                $('.blog-posts__pagination').show();


            });

        }


        /*
        *  document ready
        *
        *  This function will render each map when the document is ready (page has loaded)
        *
        *  @type	function
        *  @date	8/11/2013
        *  @since	5.0.0
        *
        *  @param	n/a
        *  @return	n/a
        */
        // global var
        var map = null;

        const geoip = document.querySelector('.js-geoip-detect-show-if');
        if (geoip) {
            const mutationObserver = new MutationObserver(callback);
            mutationObserver.observe(
                document.querySelector('.js-geoip-detect-show-if'),
                { attributeFilter: ['class']}
            ) ;
        } else {

            map_init();

        }

        function callback(mutationList) {
            map_init();  
        }

        function map_init() {

            $(document).ready(function () {

                $('.acf-map').each(function () {
                    // create map
                    map = new_map($(this));
                });

            });
        }

    })(jQuery);
</script>

<?php

get_footer();

?>